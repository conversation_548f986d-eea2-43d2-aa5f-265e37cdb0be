---
type: "manual"
---

**AI核心原则**

- **YAGNI (You Ain't Gonna Need It)**: 始终选择能满足当前需求的最简方案。除非业务复杂性明确要求，否则**禁止**主动引入CQRS、事件溯源、状态机、消息队列等复杂模式。
- **极致简洁**: 质疑任何非必要的抽象层、接口或间接调用。优先选择具体实现，仅在出现**明确的、重复的模式**或**需要替换实现**时才进行抽象。

保持 KISS 原则，优先选用能简化代码的库。避免深层嵌套；如需分支，用条件翻转提前返回。遇不明之处直接询问，勿臆测；若需求有误，立即指出。无法实现功能时说明原因。专注根因，必要时重构或重写，删除冗余代码，保持职责单一、无重复。单文件 ≤300 行，超出即拆分。尽量采用函数式平铺组合，减少递归与深层调用。非需求且无助于简化的内容一律不写。避免多余接口和臃肿设计，减少不必要的 IO 与耗时操作。优先用高性能数据结构，避免原生 dict/list/tuple。移除无用、冗余、遗留代码与接口。